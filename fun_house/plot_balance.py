import matplotlib.pyplot as plt
import numpy as np

def calculate_monthly_balance():
    """Calculate balance for monthly payment scenario"""
    # Parameters from house.py
    interest = 1.005
    take_home = 10
    rent = 3
    deposit = rent
    food = 4
    balance = -deposit
    
    n = 13
    months = []
    balances = []
    
    for i in range(1, n):
        balance -= food
        balance -= rent
        if i > 1:
            balance += take_home
        balance *= interest
        
        months.append(i)
        balances.append(balance)
    
    # Add final deposit back
    final_balance = balance + deposit
    
    return months, balances, final_balance

def calculate_quarterly_balance():
    """Calculate balance for quarterly payment scenario"""
    # Parameters from house.py
    interest = 1.005
    take_home = 10
    rent = 3
    deposit = rent
    food = 4
    balance = -deposit
    
    n = 13
    months = []
    balances = []
    
    for i in range(1, n):
        balance -= food
        if i % 3 == 1:
            balance -= rent * 3
        if i > 1:
            balance += take_home
        balance *= interest
        
        months.append(i)
        balances.append(balance)
    
    # Add final deposit back
    final_balance = balance + deposit
    
    return months, balances, final_balance

def plot_balance_comparison():
    """Plot both monthly and quarterly balance scenarios"""
    # Calculate balances for both scenarios
    months_monthly, balances_monthly, final_monthly = calculate_monthly_balance()
    months_quarterly, balances_quarterly, final_quarterly = calculate_quarterly_balance()
    
    # Create the plot
    plt.figure(figsize=(12, 8))
    
    # Plot monthly scenario
    plt.plot(months_monthly, balances_monthly, 'b-o', label='Monthly Payment', linewidth=2, markersize=6)
    
    # Plot quarterly scenario
    plt.plot(months_quarterly, balances_quarterly, 'r-s', label='Quarterly Payment', linewidth=2, markersize=6)
    
    # Add horizontal line at zero for reference
    plt.axhline(y=0, color='gray', linestyle='--', alpha=0.7, label='Break-even')
    
    # Customize the plot
    plt.xlabel('Month', fontsize=12)
    plt.ylabel('Account Balance', fontsize=12)
    plt.title('Account Balance Over Time: Monthly vs Quarterly Payments', fontsize=14, fontweight='bold')
    plt.legend(fontsize=11)
    plt.grid(True, alpha=0.3)
    
    # Add annotations for final balances
    plt.annotate(f'Final: {final_monthly:.2f}', 
                xy=(months_monthly[-1], balances_monthly[-1]), 
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.3),
                fontsize=10)
    
    plt.annotate(f'Final: {final_quarterly:.2f}', 
                xy=(months_quarterly[-1], balances_quarterly[-1]), 
                xytext=(10, -20), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.3),
                fontsize=10)
    
    # Improve layout
    plt.tight_layout()
    
    # Show the plot
    plt.show()
    
    # Print summary
    print(f"\nSummary:")
    print(f"Monthly Payment - Final Balance: {final_monthly:.2f}")
    print(f"Quarterly Payment - Final Balance: {final_quarterly:.2f}")
    print(f"Difference: {final_monthly - final_quarterly:.2f}")

if __name__ == "__main__":
    plot_balance_comparison()
