# monthly payment 
interest = 1.005
take_home = 10
rent = 3
deposit = rent
food = 4
balance = - deposit

n = 13
for i in range(1, n):
    balance -= food
    balance -= rent
    if i > 1:
        balance += take_home
    balance *= interest
    print("Account after end of month (monthly) ", i, ": ", balance)

balance += deposit
print("Account balance (monthly): ", balance)


# quarter payment
balance = - deposit
for i in range(1, n):
    balance -= food
    if i % 3 == 1:
        balance -= rent * 3
    if i > 1:
        balance += take_home
    balance *= interest
    print("Account after end of month (quarterly)", i, ": ", balance)


balance += deposit
print("Account balance (quarterly): ", balance)



